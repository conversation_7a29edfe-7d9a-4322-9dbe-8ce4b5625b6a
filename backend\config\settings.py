"""Application configuration settings."""

import os
from typing import Optional

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Application
    APP_NAME: str = "聊天系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False

    # Database
    DATABASE_URL: str = "sqlite:///./database.db"

    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60
    ALGORITHM: str = "HS256"

    # LDAP
    LDAP_URI: str = "ldap://**************:389"
    LDAP_BASE_DN: str = "dc=users,dc=appdata,dc=erayt,dc=com"
    LDAP_TIMEOUT: int = 3

    # CORS
    ALLOWED_ORIGINS: list[str] = ["http://localhost:3000", "http://localhost:5173"]

    # RAG
    MODEL_EMBEDDING_DIM: int = 1024
    MODEL_EMBEDDING: str = 'bge-m3'
    MODEL_CHAT: str = 'qwen3-32b'
    MODEL_RERANK: str = 'bge-reranker-v2-m3'
    API_KEY: str = 'sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553'
    API_URL: str = 'http://**************:23000/v1'
    API_RERANK_URL: str = 'http://**************:9997/'
    MILVUS_URL: str = 'http://**************:19530'
    MILVUS_USER: str = 'dify'
    MILVUS_PASSWORD: str = 'dify2025'

    model_config = {"env_file": ".env"}


def get_settings() -> Settings:
    """Get application settings instance."""
    return Settings()


# Global settings instance
settings = get_settings()
