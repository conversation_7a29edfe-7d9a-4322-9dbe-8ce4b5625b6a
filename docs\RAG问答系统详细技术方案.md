# RAG问答系统详细技术方案

## 1. 需求背景

### 1.1 项目概述
基于当前的聊天系统项目，需要集成RAG（检索增强生成）功能，实现智能问答能力。系统需要通过向量数据库检索相关知识，并结合大语言模型生成准确、流式的回答。

### 1.2 核心需求分析
1. **流式回答需求**：使用langchain构建RAG问答链路，通过`/chat/stream`接口实现流式回答
2. **向量检索需求**：使用`backend\services\milvus_service.py`中的`search_hybrid_parent_child`进行向量数据库检索
3. **系统集成需求**：在现有的聊天系统基础上集成RAG功能，保持现有的用户认证、会话管理等功能

### 1.3 技术目标
- 实现基于Milvus的混合检索（语义+关键词）
- 构建LangChain RAG问答链路
- 提供SSE流式响应体验
- 保持系统高可用性和扩展性

## 2. 业务架构设计

### 2.1 系统架构图

```mermaid
graph TB
    A[Vue3前端] --> B[FastAPI后端]
    B --> C[认证服务]
    B --> D[会话管理服务]
    B --> E[消息服务]
    B --> F[RAG服务]
    
    C --> G[LDAP认证]
    D --> H[SQLite数据库]
    E --> H
    
    F --> I[向量检索服务]
    F --> J[LLM服务]
    
    I --> K[Milvus向量数据库]
    J --> L[OpenAI API]
    
    subgraph "RAG处理流程"
        F --> M[1.接收用户问题]
        M --> N[2.向量检索]
        N --> O[3.文档重排序]
        O --> P[4.上下文组装]
        P --> Q[5.LLM生成]
        Q --> R[6.流式返回]
    end
```

### 2.2 核心模块设计

#### 2.2.1 RAG服务模块
```python
class RAGService:
    """RAG问答服务"""
    - milvus_retriever: MilvusRetriever  # 向量检索器
    - llm_service: LLMService           # LLM服务
    - conversation_chain: ConversationChain  # 对话链
    
    + generate_rag_response(query: str) -> Generator[str, None, None]
    + build_context(documents: List[Document]) -> str
    + create_conversation_chain() -> ConversationChain
```

#### 2.2.2 向量检索模块
```python
class MilvusRetriever(BaseRetriever):
    """基于Milvus的LangChain检索器"""
    - milvus_service: VectorStore_Search
    
    + _get_relevant_documents(query: str) -> List[Document]
    + _combine_parent_child_content(result: Dict) -> str
    + _extract_metadata(result: Dict) -> Dict
```

#### 2.2.3 LLM服务模块
```python
class LLMService:
    """大语言模型服务"""
    - client: OpenAI
    - model: str
    - temperature: float
    
    + generate_stream_response(messages: List[Dict]) -> Generator[str, None, None]
    + create_chat_completion(messages: List[Dict]) -> str
    + format_prompt(context: str, query: str) -> str
```

### 2.3 数据流架构

#### 2.3.1 RAG问答流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant API as FastAPI接口层
    participant RAG as RAG服务
    participant Retriever as 向量检索器
    participant Milvus as Milvus数据库
    participant LLM as LLM服务
    participant OpenAI as OpenAI API
    participant DB as SQLite数据库
    
    User->>API: POST /api/chat/stream
    API->>DB: 保存用户消息
    API->>RAG: generate_rag_response(query)
    
    RAG->>Retriever: get_relevant_documents(query)
    Retriever->>Milvus: search_hybrid_parent_child()
    Milvus-->>Retriever: 相关文档列表
    Retriever-->>RAG: LangChain Documents
    
    RAG->>RAG: build_context(documents)
    RAG->>LLM: generate_stream_response(context+query)
    LLM->>OpenAI: 流式请求
    
    loop 流式响应
        OpenAI-->>LLM: chunk数据
        LLM-->>RAG: chunk
        RAG-->>API: chunk
        API-->>User: SSE: data: {"content": "chunk"}
    end
    
    API->>DB: 保存AI回复
```

## 3. 接口设计

### 3.1 RAG相关接口

#### 3.1.1 流式聊天接口（已存在，需增强）
```http
POST /api/chat/stream
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "conversation_id": 123,
  "message": "请介绍一下人工智能的发展历程"
}
```

**响应（SSE流）**：
```
data: {"content": "人工智能"}
data: {"content": "的发展历程"}
data: {"content": "可以追溯到"}
...
data: [DONE]
```

**错误响应**：
```
data: {"error": "向量检索失败: 连接超时"}
data: {"error": "LLM服务异常: API配额不足"}
```

### 3.2 现有接口增强

#### 3.2.2 消息接口增强
```http
GET /api/messages?conversation_id=1
```

**响应增强**：
```json
{
  "1": [
    {
      "role": "user",
      "content": "什么是人工智能？",
      "timestamp": "2024-01-15T10:30:00Z"
    },
    {
      "role": "assistant",
      "content": "人工智能是计算机科学的一个分支...",
      "timestamp": "2024-01-15T10:30:05Z",
      "rag_context": {
        "source_count": 3,
        "retrieval_time": 0.02,
        "generation_time": 1.5,
        "sources": [
          {
            "title": "AI基础",
            "score": 0.85
          }
        ]
      }
    }
  ]
}
```

## 4. 数据模型设计

### 4.1 现有模型增强

#### 4.1.1 Message模型增强
```python
class Message(Base):
    """消息模型增强"""
    __tablename__ = "message"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversation.id"), nullable=False)
    role = Column(String, nullable=False)  # 'user' or 'assistant'
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # RAG相关字段（新增）
    rag_enabled = Column(Boolean, default=False)
    retrieval_time = Column(Float, nullable=True)  # 检索耗时（秒）
    generation_time = Column(Float, nullable=True)  # 生成耗时（秒）
    source_count = Column(Integer, nullable=True)  # 参考源数量
    rag_metadata = Column(JSON, nullable=True)  # RAG元数据
    
    # 关系
    conversation = relationship("Conversation", back_populates="messages")
    rag_sources = relationship("MessageRAGSource", back_populates="message")
```

### 4.2 新增模型

#### 4.2.1 RAG配置模型
```python
class RAGConfig(Base):
    """RAG配置模型"""
    __tablename__ = "rag_config"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, nullable=False, index=True)  # 用户级配置
    
    # 检索配置
    retrieval_k = Column(Integer, default=5)
    semantic_weight = Column(Float, default=0.7)
    min_score = Column(Float, default=0.1)
    sort_by = Column(String, default="max_score")
    
    # LLM配置
    temperature = Column(Float, default=0.7)
    max_tokens = Column(Integer, default=2000)
    model_name = Column(String, default="gpt-3.5-turbo")
    
    # 提示词模板
    prompt_template = Column(Text, default=DEFAULT_PROMPT_TEMPLATE)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

#### 4.2.2 消息RAG源模型
```python
class MessageRAGSource(Base):
    """消息RAG参考源模型"""
    __tablename__ = "message_rag_source"
    
    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey("message.id"), nullable=False)
    
    # 源文档信息
    source_id = Column(String, nullable=False)  # Milvus文档ID
    source_title = Column(String, nullable=True)
    source_content = Column(Text, nullable=False)
    source_score = Column(Float, nullable=False)
    
    # 元数据
    metadata = Column(JSON, nullable=True)
    rank = Column(Integer, nullable=False)  # 检索排名
    
    # 关系
    message = relationship("Message", back_populates="rag_sources")
```

#### 4.2.3 RAG查询日志模型
```python
class RAGQueryLog(Base):
    """RAG查询日志模型"""
    __tablename__ = "rag_query_log"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, nullable=False, index=True)
    conversation_id = Column(Integer, ForeignKey("conversation.id"), nullable=True)
    
    # 查询信息
    query = Column(Text, nullable=False)
    query_type = Column(String, default="chat")  # chat, search
    
    # 性能指标
    retrieval_time = Column(Float, nullable=False)
    generation_time = Column(Float, nullable=True)
    total_time = Column(Float, nullable=False)
    
    # 结果统计
    retrieved_count = Column(Integer, nullable=False)
    source_count = Column(Integer, nullable=False)
    
    # 状态
    status = Column(String, nullable=False)  # success, error
    error_message = Column(Text, nullable=True)
    
    # 时间戳
    timestamp = Column(DateTime, default=datetime.utcnow)
```

### 4.3 Schema模型

#### 4.3.1 RAG请求响应Schema
```python
# RAG相关Schema
class RAGConfigSchema(BaseModel):
    """RAG配置Schema"""
    retrieval_k: int = 5
    semantic_weight: float = 0.7
    min_score: float = 0.1
    sort_by: str = "max_score"
    temperature: float = 0.7
    max_tokens: int = 2000
    model_name: str = "gpt-3.5-turbo"
    prompt_template: str

class KnowledgeSearchRequest(BaseModel):
    """知识库搜索请求"""
    query: str = Field(..., description="搜索查询")
    k: int = Field(default=5, ge=1, le=20, description="返回结果数量")
    min_score: float = Field(default=0.1, ge=0, le=1, description="最小相似度")
    filter_conditions: Optional[Dict[str, Any]] = None

class KnowledgeSearchResult(BaseModel):
    """知识库搜索结果"""
    id: str
    title: Optional[str]
    content: str
    score: float
    metadata: Optional[Dict[str, Any]]

class KnowledgeSearchResponse(BaseModel):
    """知识库搜索响应"""
    results: List[KnowledgeSearchResult]
    total: int
    query_time: float

class RAGSourceInfo(BaseModel):
    """RAG源信息"""
    title: Optional[str]
    score: float
    rank: int

class RAGMetadata(BaseModel):
    """RAG元数据"""
    source_count: int
    retrieval_time: float
    generation_time: float
    sources: List[RAGSourceInfo]
```

#### 4.3.2 增强的消息Schema
```python
class MessageResponseEnhanced(BaseModel):
    """增强的消息响应Schema"""
    role: str
    content: str
    timestamp: datetime
    rag_enabled: bool = False
    rag_context: Optional[RAGMetadata] = None

    model_config = {"from_attributes": True}

class ChatRequestEnhanced(BaseModel):
    """增强的聊天请求Schema"""
    conversation_id: int
    message: str
    rag_enabled: bool = True
    rag_config: Optional[RAGConfigSchema] = None
```

## 5. SQL设计

### 5.1 数据库表结构

#### 5.1.1 现有表结构修改

```sql
-- 修改message表，添加RAG相关字段
ALTER TABLE message ADD COLUMN rag_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE message ADD COLUMN retrieval_time REAL;
ALTER TABLE message ADD COLUMN generation_time REAL;
ALTER TABLE message ADD COLUMN source_count INTEGER;
ALTER TABLE message ADD COLUMN rag_metadata JSON;

-- 修改conversation表，添加RAG相关字段
ALTER TABLE conversation ADD COLUMN rag_enabled BOOLEAN DEFAULT TRUE;
ALTER TABLE conversation ADD COLUMN last_activity DATETIME DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE conversation ADD COLUMN message_count INTEGER DEFAULT 0;
```

#### 5.1.2 新增表结构

```sql
-- RAG配置表
CREATE TABLE rag_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL,
    retrieval_k INTEGER DEFAULT 5,
    semantic_weight REAL DEFAULT 0.7,
    min_score REAL DEFAULT 0.1,
    sort_by VARCHAR(20) DEFAULT 'max_score',
    temperature REAL DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 2000,
    model_name VARCHAR(50) DEFAULT 'gpt-3.5-turbo',
    prompt_template TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(username),
    INDEX idx_username (username)
);

-- 消息RAG源表
CREATE TABLE message_rag_source (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    message_id INTEGER NOT NULL,
    source_id VARCHAR(255) NOT NULL,
    source_title VARCHAR(500),
    source_content TEXT NOT NULL,
    source_score REAL NOT NULL,
    metadata JSON,
    rank INTEGER NOT NULL,
    
    FOREIGN KEY (message_id) REFERENCES message(id) ON DELETE CASCADE,
    INDEX idx_message_id (message_id),
    INDEX idx_source_score (source_score DESC)
);

-- RAG查询日志表
CREATE TABLE rag_query_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL,
    conversation_id INTEGER,
    query TEXT NOT NULL,
    query_type VARCHAR(20) DEFAULT 'chat',
    retrieval_time REAL NOT NULL,
    generation_time REAL,
    total_time REAL NOT NULL,
    retrieved_count INTEGER NOT NULL,
    source_count INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES conversation(id) ON DELETE SET NULL,
    INDEX idx_username (username),
    INDEX idx_timestamp (timestamp),
    INDEX idx_status (status)
);
```

### 5.2 索引设计

```sql
-- 性能优化索引
CREATE INDEX idx_message_rag_enabled ON message(rag_enabled);
CREATE INDEX idx_message_timestamp_desc ON message(timestamp DESC);
CREATE INDEX idx_conversation_last_activity ON conversation(last_activity DESC);
CREATE INDEX idx_rag_query_log_total_time ON rag_query_log(total_time);
CREATE INDEX idx_message_rag_source_score ON message_rag_source(source_score DESC, rank);

-- 复合索引
CREATE INDEX idx_message_conversation_timestamp ON message(conversation_id, timestamp DESC);
CREATE INDEX idx_rag_log_user_time ON rag_query_log(username, timestamp DESC);
```

### 5.3 数据迁移脚本

```sql
-- 数据迁移脚本
-- 1. 为现有会话启用RAG功能
UPDATE conversation SET rag_enabled = TRUE WHERE rag_enabled IS NULL;

-- 2. 更新会话消息统计
UPDATE conversation 
SET message_count = (
    SELECT COUNT(*) 
    FROM message 
    WHERE message.conversation_id = conversation.id
);

-- 3. 更新会话最后活动时间
UPDATE conversation 
SET last_activity = (
    SELECT MAX(timestamp) 
    FROM message 
    WHERE message.conversation_id = conversation.id
)
WHERE EXISTS (
    SELECT 1 FROM message WHERE message.conversation_id = conversation.id
);

-- 4. 为每个用户创建默认RAG配置
INSERT INTO rag_config (username, prompt_template)
SELECT DISTINCT username, 
    '基于以下内容回答问题，如果内容不足以回答问题，请说明：

{context}

问题：{question}
回答：'
FROM conversation
WHERE username NOT IN (SELECT username FROM rag_config);
```

## 6. 核心服务实现

### 6.1 RAG服务实现

```python
class RAGService:
    """RAG问答服务实现"""
    
    def __init__(self):
        self.milvus_retriever = MilvusRetriever(
            milvus_service=self._get_milvus_service()
        )
        self.llm_service = LLMService()
        self.conversation_chain = self._create_conversation_chain()
    
    async def generate_rag_response(
        self, 
        query: str, 
        conversation_id: int,
        rag_config: Optional[RAGConfigSchema] = None
    ) -> AsyncGenerator[str, None]:
        """生成RAG流式响应"""
        start_time = time.time()
        
        try:
            # 1. 向量检索
            retrieval_start = time.time()
            documents = await self._retrieve_documents(query, rag_config)
            retrieval_time = time.time() - retrieval_start
            
            # 2. 构建上下文
            context = self._build_context(documents)
            
            # 3. 构建提示词
            prompt = self._format_prompt(context, query, rag_config)
            
            # 4. 流式生成
            generation_start = time.time()
            accumulated_response = ""
            
            async for chunk in self.llm_service.generate_stream_response(prompt):
                accumulated_response += chunk
                yield chunk
            
            generation_time = time.time() - generation_start
            total_time = time.time() - start_time
            
            # 5. 保存RAG元数据
            await self._save_rag_metadata(
                conversation_id=conversation_id,
                query=query,
                documents=documents,
                retrieval_time=retrieval_time,
                generation_time=generation_time,
                total_time=total_time
            )
            
        except Exception as e:
            await self._log_rag_error(query, str(e))
            yield f"抱歉，处理您的问题时出现错误：{str(e)}"
```

### 6.2 向量检索服务实现

```python
class MilvusRetriever(BaseRetriever):
    """Milvus检索器实现"""
    
    def __init__(self, milvus_service: VectorStore_Search):
        super().__init__()
        self.milvus_service = milvus_service
    
    def _get_relevant_documents(self, query: str) -> List[Document]:
        """获取相关文档"""
        try:
            # 使用search_hybrid_parent_child进行检索
            results = self.milvus_service.search_hybrid_parent_child(
                query=query,
                k=5,
                semantic_weight=0.7,
                min_score=0.1,
                sort_by="max_score"
            )
            
            # 转换为LangChain Document格式
            documents = []
            for i, result in enumerate(results):
                content = self._combine_parent_child_content(result)
                metadata = self._extract_metadata(result, i)
                documents.append(Document(page_content=content, metadata=metadata))
            
            return documents
            
        except Exception as e:
            logger.error(f"向量检索失败: {e}")
            return []
    
    def _combine_parent_child_content(self, result: Dict) -> str:
        """组合父子文档内容"""
        parent_content = result.get('parent_document', '')
        child_contents = result.get('child_documents', [])
        
        if not child_contents:
            return parent_content
        
        combined = f"主要内容：\n{parent_content}\n\n相关片段：\n"
        for i, child in enumerate(child_contents, 1):
            combined += f"{i}. {child.get('document', '')}\n"
        
        return combined
    
    def _extract_metadata(self, result: Dict, rank: int) -> Dict:
        """提取元数据"""
        return {
            'source_id': result.get('id', ''),
            'score': result.get('score', 0.0),
            'rank': rank,
            'title': result.get('title', ''),
            'source': result.get('metadata', {}).get('source', ''),
            'page': result.get('metadata', {}).get('page', 0)
        }
```

### 6.3 LLM服务实现

```python
class LLMService:
    """LLM服务实现"""
    
    def __init__(self):
        self.client = OpenAI(
            api_key=settings.API_KEY,
            base_url=settings.API_URL
        )
    
    async def generate_stream_response(
        self, 
        prompt: str,
        model: str = None,
        temperature: float = None
    ) -> AsyncGenerator[str, None]:
        """生成流式响应"""
        try:
            response = await self.client.chat.completions.create(
                model=model or settings.MODEL_CHAT,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature or 0.7,
                stream=True,
                max_tokens=2000
            )
            
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"LLM生成失败: {e}")
            yield f"生成回答时出现错误: {str(e)}"
    
    def format_prompt(
        self, 
        context: str, 
        query: str, 
        template: str = None
    ) -> str:
        """格式化提示词"""
        default_template = """基于以下内容回答问题，如果内容不足以回答问题，请说明：

{context}

问题：{question}
回答："""
        
        template = template or default_template
        return template.format(context=context, question=query)
```

## 7. API路由实现

### 7.1 增强的聊天接口

```python
@router.post("/chat/stream", summary="RAG流式聊天")
async def rag_chat_stream(
    chat_data: ChatRequestEnhanced,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
    rag_service: RAGService = Depends(get_rag_service)
):
    """RAG增强的流式聊天接口"""
    
    async def generate_response():
        try:
            # 1. 保存用户消息
            user_message = message_service.create_user_message(
                db, chat_data.conversation_id, chat_data.message, current_username
            )
            
            # 2. 生成RAG回复
            ai_response_content = ""
            
            if chat_data.rag_enabled:
                # 使用RAG服务
                async for chunk in rag_service.generate_rag_response(
                    query=chat_data.message,
                    conversation_id=chat_data.conversation_id,
                    rag_config=chat_data.rag_config
                ):
                    ai_response_content += chunk
                    yield f'data: {{"content": "{chunk}"}}\n\n'
            else:
                # 使用原有服务
                for chunk in message_service.generate_chat_response(chat_data.message):
                    ai_response_content += chunk
                    yield f'data: {{"content": "{chunk}"}}\n\n'
            
            # 3. 保存AI回复
            message_service.create_assistant_message(
                db, chat_data.conversation_id, ai_response_content, 
                rag_enabled=chat_data.rag_enabled
            )
            
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            yield f'data: {{"error": "{str(e)}"}}\n\n'
    
    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
        }
    )
```

### 7.2 知识库查询接口

```python
@router.post("/knowledge/search", summary="知识库查询")
async def search_knowledge(
    request: KnowledgeSearchRequest,
    current_username: str = Depends(get_current_username),
    rag_service: RAGService = Depends(get_rag_service)
) -> KnowledgeSearchResponse:
    """知识库查询接口"""
    start_time = time.time()
    
    try:
        # 执行向量检索
        documents = rag_service.milvus_retriever._get_relevant_documents(request.query)
        
        # 格式化结果
        results = []
        for doc in documents:
            results.append(KnowledgeSearchResult(
                id=doc.metadata.get('source_id', ''),
                title=doc.metadata.get('title'),
                content=doc.page_content[:500] + "..." if len(doc.page_content) > 500 else doc.page_content,
                score=doc.metadata.get('score', 0.0),
                metadata=doc.metadata
            ))
        
        query_time = time.time() - start_time
        
        return KnowledgeSearchResponse(
            results=results,
            total=len(results),
            query_time=query_time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"知识库查询失败: {str(e)}"
        )
```

### 7.3 RAG配置管理接口

```python
@router.get("/rag/config", summary="获取RAG配置")
async def get_rag_config(
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session)
) -> RAGConfigSchema:
    """获取用户RAG配置"""
    config = db.query(RAGConfig).filter(
        RAGConfig.username == current_username
    ).first()
    
    if not config:
        # 创建默认配置
        config = RAGConfig(username=current_username)
        db.add(config)
        db.commit()
        db.refresh(config)
    
    return RAGConfigSchema.model_validate(config)

@router.put("/rag/config", summary="更新RAG配置")
async def update_rag_config(
    config_data: RAGConfigSchema,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session)
) -> RAGConfigSchema:
    """更新用户RAG配置"""
    config = db.query(RAGConfig).filter(
        RAGConfig.username == current_username
    ).first()
    
    if not config:
        config = RAGConfig(username=current_username)
        db.add(config)
    
    # 更新配置
    for field, value in config_data.model_dump().items():
        setattr(config, field, value)
    
    config.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(config)
    
    return RAGConfigSchema.model_validate(config)
```

## 8. 前端集成方案

### 8.1 Chat Store增强

```typescript
// stores/chat.ts 增强
export const useChatStore = defineStore('chat', () => {
  // ... 现有状态

  // RAG相关状态
  const ragEnabled = ref(true)
  const ragConfig = ref<RAGConfig>({
    retrieval_k: 5,
    semantic_weight: 0.7,
    min_score: 0.1,
    temperature: 0.7,
    max_tokens: 2000
  })

  // RAG流式聊天
  const sendRAGMessage = async (conversationId: number, content: string) => {
    const userMessage: Message = {
      role: 'user',
      content,
      timestamp: new Date().toISOString()
    }

    if (!messages.value[conversationId]) {
      messages.value[conversationId] = []
    }
    messages.value[conversationId].push(userMessage)

    try {
      // 创建助手消息占位符
      const assistantMessage: Message = {
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString(),
        rag_context: {
          source_count: 0,
          retrieval_time: 0,
          generation_time: 0,
          sources: []
        }
      }
      messages.value[conversationId].push(assistantMessage)
      
      // 发送SSE请求
      const response = await fetch('/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          conversation_id: conversationId,
          message: content,
          rag_enabled: ragEnabled.value,
          rag_config: ragConfig.value
        })
      })

      const reader = response.body?.getReader()
      if (!reader) throw new Error('无法读取响应流')

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') return
            
            try {
              const parsed = JSON.parse(data)
              if (parsed.content) {
                // 更新消息内容
                const lastMessage = messages.value[conversationId][messages.value[conversationId].length - 1]
                lastMessage.content += parsed.content
              }
            } catch (e) {
              console.error('解析SSE数据失败:', e)
            }
          }
        }
      }
      
    } catch (error) {
      console.error('发送RAG消息失败:', error)
    }
  }

  return {
    // ... 现有返回
    ragEnabled,
    ragConfig,
    sendRAGMessage
  }
})
```

### 8.2 RAG配置组件

```vue
<!-- components/RAGConfig.vue -->
<template>
  <div class="rag-config">
    <h3>RAG配置</h3>
    
    <div class="config-group">
      <label>检索数量</label>
      <input v-model.number="config.retrieval_k" type="number" min="1" max="20" />
    </div>
    
    <div class="config-group">
      <label>语义权重</label>
      <input v-model.number="config.semantic_weight" type="range" min="0" max="1" step="0.1" />
      <span>{{ config.semantic_weight }}</span>
    </div>
    
    <div class="config-group">
      <label>最小相似度</label>
      <input v-model.number="config.min_score" type="range" min="0" max="1" step="0.05" />
      <span>{{ config.min_score }}</span>
    </div>
    
    <div class="config-group">
      <label>生成温度</label>
      <input v-model.number="config.temperature" type="range" min="0" max="2" step="0.1" />
      <span>{{ config.temperature }}</span>
    </div>
    
    <button @click="saveConfig">保存配置</button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useChatStore } from '@/stores/chat'

const chatStore = useChatStore()
const config = ref({ ...chatStore.ragConfig })

const loadConfig = async () => {
  try {
    const response = await fetch('/api/rag/config', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    const data = await response.json()
    config.value = data
    chatStore.ragConfig = data
  } catch (error) {
    console.error('加载RAG配置失败:', error)
  }
}

const saveConfig = async () => {
  try {
    await fetch('/api/rag/config', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify(config.value)
    })
    chatStore.ragConfig = { ...config.value }
    alert('配置保存成功')
  } catch (error) {
    console.error('保存RAG配置失败:', error)
  }
}

onMounted(() => {
  loadConfig()
})
</script>
```

## 9. 部署和运维

### 9.1 环境配置

```bash
# .env 文件增强
# RAG相关配置
MODEL_EMBEDDING_DIM=1024
MODEL_EMBEDDING=bge-m3
MODEL_CHAT=qwen3-32b
MODEL_RERANK=bge-reranker-v2-m3
API_KEY=your-openai-api-key
API_URL=http://**************:23000/v1
API_RERANK_URL=http://**************:9997/
MILVUS_URL=http://**************:19530
MILVUS_USER=dify
MILVUS_PASSWORD=dify2025
MILVUS_DB_NAME=erayt_wiki
MILVUS_COLLECTION_NAME=rcs

# 性能配置
RAG_CACHE_TTL=3600
RAG_MAX_RETRIES=3
RAG_TIMEOUT=30
```

### 9.2 Docker配置

```dockerfile
# Dockerfile 增强
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装Python依赖
RUN pip install uv && uv sync --frozen

# 复制应用代码
COPY . .

# 创建数据目录
RUN mkdir -p /app/data

# 设置环境变量
ENV PYTHONPATH=/app
ENV DATABASE_URL=sqlite:///./data/database.db

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 9.3 监控和日志

```python
# utils/monitoring.py
import time
import logging
from functools import wraps
from typing import Callable, Any

logger = logging.getLogger(__name__)

def monitor_rag_performance(func: Callable) -> Callable:
    """RAG性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"RAG操作 {func.__name__} 执行成功，耗时: {execution_time:.2f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"RAG操作 {func.__name__} 执行失败，耗时: {execution_time:.2f}秒，错误: {str(e)}")
            raise
    return wrapper

# 性能指标收集
class RAGMetrics:
    def __init__(self):
        self.query_count = 0
        self.total_retrieval_time = 0
        self.total_generation_time = 0
        self.error_count = 0
    
    def record_query(self, retrieval_time: float, generation_time: float, success: bool = True):
        self.query_count += 1
        self.total_retrieval_time += retrieval_time
        self.total_generation_time += generation_time
        if not success:
            self.error_count += 1
    
    def get_stats(self) -> dict:
        if self.query_count == 0:
            return {"message": "暂无查询数据"}
        
        return {
            "total_queries": self.query_count,
            "avg_retrieval_time": self.total_retrieval_time / self.query_count,
            "avg_generation_time": self.total_generation_time / self.query_count,
            "error_rate": self.error_count / self.query_count,
            "success_rate": (self.query_count - self.error_count) / self.query_count
        }
```

## 10. 测试方案

### 10.1 单元测试

```python
# tests/test_rag_service.py
import pytest
from unittest.mock import Mock, AsyncMock
from services.rag_service import RAGService

class TestRAGService:
    
    @pytest.fixture
    def rag_service(self):
        service = RAGService()
        service.milvus_retriever = Mock()
        service.llm_service = Mock()
        return service
    
    @pytest.mark.asyncio
    async def test_generate_rag_response_success(self, rag_service):
        # 模拟检索结果
        mock_documents = [
            Mock(page_content="测试文档内容", metadata={"score": 0.8})
        ]
        rag_service.milvus_retriever._get_relevant_documents.return_value = mock_documents
        
        # 模拟LLM响应
        async def mock_stream():
            for chunk in ["这是", "一个", "测试", "回答"]:
                yield chunk
        
        rag_service.llm_service.generate_stream_response = mock_stream
        
        # 执行测试
        response_chunks = []
        async for chunk in rag_service.generate_rag_response("测试问题", 1):
            response_chunks.append(chunk)
        
        assert response_chunks == ["这是", "一个", "测试", "回答"]
    
    @pytest.mark.asyncio
    async def test_generate_rag_response_retrieval_error(self, rag_service):
        # 模拟检索失败
        rag_service.milvus_retriever._get_relevant_documents.side_effect = Exception("检索失败")
        
        # 执行测试
        response_chunks = []
        async for chunk in rag_service.generate_rag_response("测试问题", 1):
            response_chunks.append(chunk)
        
        assert any("错误" in chunk for chunk in response_chunks)
```

### 10.2 集成测试

```python
# tests/test_rag_api.py
import pytest
from fastapi.testclient import TestClient
from main import app

class TestRAGAPI:
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        # 获取测试用的认证token
        return {"Authorization": "Bearer test_token"}
    
    def test_chat_stream_with_rag(self, client, auth_headers):
        response = client.post(
            "/api/chat/stream",
            json={
                "conversation_id": 1,
                "message": "什么是人工智能？",
                "rag_enabled": True
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
    
    def test_knowledge_search(self, client, auth_headers):
        response = client.post(
            "/api/knowledge/search",
            json={
                "query": "人工智能",
                "k": 5,
                "min_score": 0.1
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "results" in data
        assert "total" in data
        assert "query_time" in data
```

### 10.3 性能测试

```python
# tests/test_rag_performance.py
import asyncio
import time
from services.rag_service import RAGService

class TestRAGPerformance:
    
    @pytest.mark.asyncio
    async def test_concurrent_rag_requests(self):
        """测试并发RAG请求性能"""
        rag_service = RAGService()
        
        async def single_request():
            start_time = time.time()
            chunks = []
            async for chunk in rag_service.generate_rag_response("测试问题", 1):
                chunks.append(chunk)
            return time.time() - start_time, len(chunks)
        
        # 并发执行10个请求
        tasks = [single_request() for _ in range(10)]
        results = await asyncio.gather(*tasks)
        
        # 检查性能指标
        avg_time = sum(result[0] for result in results) / len(results)
        assert avg_time < 5.0  # 平均响应时间应小于5秒
        
        total_chunks = sum(result[1] for result in results)
        assert total_chunks > 0  # 应该有回答内容
```

## 11. 总结

本技术方案提供了一个完整的RAG问答系统实现，包括：

1. **完整的架构设计**：从前端到后端的完整技术栈
2. **详细的API接口**：涵盖所有必要的功能接口
3. **完善的数据模型**：支持RAG功能的数据库设计
4. **核心服务实现**：RAG服务、向量检索、LLM集成
5. **前端集成方案**：Vue3组件和状态管理
6. **部署和运维**：Docker配置、监控、日志
7. **测试方案**：单元测试、集成测试、性能测试

该方案可以直接用于生成可执行的代码，实现基于LangChain和Milvus的RAG问答系统。 