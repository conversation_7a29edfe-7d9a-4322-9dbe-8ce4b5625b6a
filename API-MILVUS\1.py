import requests
import json


def main(class_tag: str, collection_name: str, query: str) -> dict:
    filter_conditions = {}

    if collection_name == "金融研究室":
        collection_name = "FinancialResearchOffice"
    elif collection_name == "RCS相关文档":
        collection_name = "rcs"
        filter_conditions["class_name"] = class_tag

    url = "http://192.168.193.66:18002/search/hybrid-parent-child"
    # 请求头
    headers = {"Content-Type": "application/json", "Accept": "application/json"}

    semantic_weight = 0.7
    if len(query) < 6:
        semantic_weight = 0.5

    # 请求体数据
    payload = {
        "query": query,
        "k": 5,
        "filter_conditions": filter_conditions,
        "min_score": 0,
        "sort_by": "max_score",
        "semantic_weight": semantic_weight,
        "collection_name": collection_name,
    }

    # 发送POST请求
    response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=10)

    # 检查响应状态
    response.raise_for_status()

    # 解析JSON响应
    result = response.json()

    out_ref_data = ""
    ref_i = 1
    for data in result["data"]:
        ref_doc = data["parent_metadata"]["filename"]
        ref_version = data["parent_metadata"]["version"]
        temp_out = (
            out_ref_data
            + f"# 参考资料{ref_i}\n\n- 来源：{ref_doc}\n\n```markdown\n{data['parent_document']}\n```\n\n"
        )
        if len(temp_out) > 15000:
            break
        out_ref_data = temp_out

        ref_i += 1
    return {
        "result": out_ref_data,
    }
