"""Message service for managing chat messages."""

from typing import List, Generator, Dict
from sqlalchemy.orm import Session

from models.message import Message
from crud.conversation_dao import ConversationDao
from crud.message_dao import MessageDao
from schemas import ChatRequest
from services.llm_service import get_llm_service


class MessageService:

    def get_conversation_messages(
            self, db: Session, conversation_id: int, username: str
    ) -> List[Message]:
        """
        获取指定会话的所有消息

        Args:
            db: Database session
            conversation_id: 会话ID
            username: 用户名（用于权限验证）

        Returns:
            List[Message]: 会话中的消息列表
        """
        conversation = ConversationDao.get_by_id(db, conversation_id, username)
        if not conversation:
            return []
        return MessageDao.get_by_conversation_id(db, conversation_id)

    def create_user_message(
            self, db: Session, conversation_id: int, content: str, username: str
    ) -> Message:
        """
        创建用户消息

        Args:
            db: Database session
            conversation_id: 会话ID
            content: 消息内容
            username: 用户名（用于权限验证）

        Returns:
            Message: 创建的消息对象
        """
        conversation = ConversationDao.get_by_id(db, conversation_id, username)
        if not conversation:
            raise ValueError("会话不存在或无权限访问")
        return MessageDao.create(db, conversation_id, "user", content)

    def create_assistant_message(
            self, db: Session, conversation_id: int, content: str
    ) -> Message:
        """
        创建助手消息

        Args:
            db: Database session
            conversation_id: 会话ID
            content: 消息内容

        Returns:
            Message: 创建的消息对象
        """
        message = Message(
            conversation_id=conversation_id, role="assistant", content=content
        )
        db.add(message)
        db.commit()
        db.refresh(message)
        return message

    def generate_chat_response(self, chat_data: ChatRequest) -> Generator[str, None, None]:
        """
        生成流式聊天回复（集成RAG）
        """
        # 使用LLM服务生成RAG回复
        llm_service = get_llm_service()

        yield from llm_service.generate_chat_response(chat_data.message, chat_data.collection_name, chat_data.input)

    def get_messages_by_conversation_id(
            self, db: Session, conversation_id: int, username: str
    ) -> Dict[int, List[Message]]:
        """
        获取格式化的消息数据

        Args:
            db: Database session
            conversation_id: 会话ID
            username: 用户名（用于权限验证）

        Returns:
            Dict[int, List[Message]]: 以会话ID为键的消息字典
        """
        messages = self.get_conversation_messages(db, conversation_id, username)
        return {conversation_id: messages}
