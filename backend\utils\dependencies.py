"""Application dependencies and dependency injection."""

from functools import lru_cache
from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt

from crud.database import get_database_session
from services.auth_service import AuthService
from services.llm_service import LLMService, get_llm_service
from rag.vectorstore_search import VectorStore_Search
from config.settings import settings

# 安全相关
security = HTTPBearer()
auth_service = AuthService()


async def get_current_username(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> str:
    """获取当前认证用户名"""
    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM],
        )
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return username
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )


def get_llm_service_instance() -> LLMService:
    """获取LLM服务实例"""
    return get_llm_service()


# 类型注解
MilvusService = Annotated[VectorStore_Search, Depends(get_milvus_service)]
LLMServiceDep = Annotated[LLMService, Depends(get_llm_service_instance)]
DatabaseSession = Annotated[Session, Depends(get_database_session)]
CurrentUsername = Annotated[str, Depends(get_current_username)]
